//! 考试指引API处理器
//!
//! 处理考试指引相关的HTTP请求

use std::sync::Arc;
use actix_web::{web, HttpResponse, Result as ActixResult};
use serde::Deserialize;
use tracing::{info, error};

use recommendation_core::application::exam::{
    ExamApplicationService,
    ExamGuideRequestDto,
};
use recommendation_core::domain::exam::ExamType;
use recommendation_core::models::ErrorCode;
use crate::http::response;

/// 获取考试指引查询参数
#[derive(Debug, Deserialize)]
pub struct GetExamGuideQuery {
    /// 用户ID
    pub user_id: i64,
    /// 考试类型：reading（阅读）、math（数学）、full（完整考试），默认为 full
    pub exam_type: Option<ExamType>,
    /// 可选：现有会话ID（用于学科指引时不创建新会话）
    pub session_id: Option<String>,
}

/// 获取考试指引
///
/// GET /api/v1/exam/guide?user_id=1&exam_type=reading
pub async fn get_exam_guide(
    query: web::Query<GetExamGuideQuery>,
    exam_service: web::Data<Option<Arc<dyn ExamApplicationService>>>,
) -> ActixResult<HttpResponse> {
    // 如果没有指定考试类型，默认使用 Full
    let exam_type = query.exam_type.unwrap_or(ExamType::Full);

    info!("收到获取考试指引请求: 用户ID={}, 考试类型={:?}, 会话ID={:?}",
          query.user_id, exam_type, query.session_id);

    // 构建应用服务请求
    let app_request = ExamGuideRequestDto {
        user_id: query.user_id,
        exam_type: exam_type.clone(),
        subject: None, // 不再使用 subject 参数，直接通过 exam_type 区分
        session_id: query.session_id.clone(),
    };

    // 调用应用服务
    let service = exam_service.as_ref().as_ref()
        .ok_or_else(|| {
            error!("考试服务未初始化");
            actix_web::error::ErrorInternalServerError("考试服务未初始化")
        })?;

    match service.get_exam_guide(app_request).await {
        Ok(response) => {
            info!("成功获取考试指引: 用户ID={}, 会话ID={}, 考试类型={:?}, 总题数={}, 总时间={}分钟",
                  query.user_id, response.session_id, response.exam_type,
                  response.total_questions, response.total_time_minutes);
            Ok(response::success(response))
        }
        Err(e) => {
            error!("获取考试指引失败: 用户ID={}, 考试类型={:?}, 错误: {:?}",
                   query.user_id, exam_type, e);
            Ok(response::error_empty(
                ErrorCode::InternalError,
                Some(format!("获取考试指引失败: {}", e)),
            ))
        }
    }
}
