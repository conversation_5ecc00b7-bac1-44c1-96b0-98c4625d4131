//! 考试答题记录仓储实现
//!
//! 实现考试答题记录的数据访问逻辑

use async_trait::async_trait;
use sea_orm::{DatabaseConnection, EntityTrait, QueryFilter, ColumnTrait, Set, ActiveModelTrait, QueryOrder};
use tracing::{info, error};
use chrono::Utc;

use crate::error::Result;
use crate::domain::exam::{ExamAnswer, ExamAnswerRepository, ModuleType};
use crate::infrastructure::database::models::exam_answer::{Entity as ExamAnswerEntity, Model as ExamAnswerModel, ActiveModel as ExamAnswerActiveModel};

/// 考试答题记录仓储实现
pub struct ExamAnswerRepositoryImpl {
    db: DatabaseConnection,
}

impl ExamAnswerRepositoryImpl {
    /// 创建新的考试答题记录仓储实例
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl ExamAnswerRepository for ExamAnswerRepositoryImpl {
    async fn create(&self, answer: &ExamAnswer) -> Result<()> {
        info!("创建答题记录: 会话ID={}, 题目ID={}",
              answer.session_id, answer.question_id);

        let active_model = ExamAnswerActiveModel {
            session_id: Set(answer.session_id.clone()),
            user_id: Set(answer.user_id),
            paper_id: Set(answer.paper_id),
            question_id: Set(answer.question_id),
            module_type: Set(answer.module_type.to_string()),
            module_sequence: Set(answer.module_sequence),
            user_answer: Set(answer.user_answer.clone()),
            is_correct: Set(answer.is_correct),
            response_time_seconds: Set(answer.response_time_seconds),
            answer_status: Set(if answer.user_answer.is_some() {
                "answered".to_string()
            } else {
                "unanswered".to_string()
            }),
            created_at: Set(Utc::now().into()),
            updated_at: Set(Utc::now().into()),
            ..Default::default()
        };

        match ExamAnswerEntity::insert(active_model).exec(&self.db).await {
            Ok(_) => {
                info!("成功创建答题记录: 会话ID={}, 题目ID={}",
                      answer.session_id, answer.question_id);
                Ok(())
            }
            Err(e) => {
                error!("创建答题记录失败: {:?}", e);
                Err(crate::error::Error::database(format!("创建答题记录失败: {}", e)))
            }
        }
    }

    async fn batch_create(&self, answers: &[ExamAnswer]) -> Result<()> {
        info!("批量创建答题记录: 数量={}", answers.len());

        for answer in answers {
            self.create(answer).await?;
        }

        info!("成功批量创建答题记录: 数量={}", answers.len());
        Ok(())
    }

    async fn update(&self, answer: &ExamAnswer) -> Result<()> {
        info!("更新答题记录: 会话ID={}, 题目ID={}",
              answer.session_id, answer.question_id);

        // 先查找现有记录
        let existing = ExamAnswerEntity::find()
            .filter(crate::infrastructure::database::models::exam_answer::Column::SessionId.eq(&answer.session_id))
            .filter(crate::infrastructure::database::models::exam_answer::Column::QuestionId.eq(answer.question_id))
            .one(&self.db)
            .await
            .map_err(|e| crate::error::Error::database(format!("查询答题记录失败: {}", e)))?;

        if let Some(existing_model) = existing {
            let mut active_model: ExamAnswerActiveModel = existing_model.into();
            active_model.user_answer = Set(answer.user_answer.clone());
            active_model.is_correct = Set(answer.is_correct);
            active_model.response_time_seconds = Set(answer.response_time_seconds);
            active_model.answer_status = Set(if answer.user_answer.is_some() {
                "answered".to_string()
            } else {
                "unanswered".to_string()
            });
            active_model.updated_at = Set(Utc::now().into());

            match active_model.update(&self.db).await {
                Ok(_) => {
                    info!("成功更新答题记录: 会话ID={}, 题目ID={}",
                          answer.session_id, answer.question_id);
                    Ok(())
                }
                Err(e) => {
                    error!("更新答题记录失败: {:?}", e);
                    Err(crate::error::Error::database(format!("更新答题记录失败: {}", e)))
                }
            }
        } else {
            // 如果记录不存在，则创建新记录
            self.create(answer).await
        }
    }

    async fn find_by_session_id(&self, session_id: &str) -> Result<Vec<ExamAnswer>> {
        info!("查找会话的所有答题记录: 会话ID={}", session_id);

        let models = ExamAnswerEntity::find()
            .filter(crate::infrastructure::database::models::exam_answer::Column::SessionId.eq(session_id))
            .order_by_asc(crate::infrastructure::database::models::exam_answer::Column::ModuleSequence)
            .all(&self.db)
            .await
            .map_err(|e| crate::error::Error::database(format!("查询答题记录失败: {}", e)))?;

        let answers: Vec<ExamAnswer> = models.into_iter()
            .map(|model| self.convert_model_to_domain(model))
            .collect();

        info!("找到 {} 条答题记录", answers.len());
        Ok(answers)
    }

    async fn find_by_session_and_question(
        &self,
        session_id: &str,
        question_id: i32,
    ) -> Result<Option<ExamAnswer>> {
        info!("查找特定题目的答题记录: 会话ID={}, 题目ID={}", session_id, question_id);

        let model = ExamAnswerEntity::find()
            .filter(crate::infrastructure::database::models::exam_answer::Column::SessionId.eq(session_id))
            .filter(crate::infrastructure::database::models::exam_answer::Column::QuestionId.eq(question_id))
            .one(&self.db)
            .await
            .map_err(|e| crate::error::Error::database(format!("查询答题记录失败: {}", e)))?;

        if let Some(model) = model {
            info!("找到答题记录");
            Ok(Some(self.convert_model_to_domain(model)))
        } else {
            info!("未找到答题记录");
            Ok(None)
        }
    }

    async fn find_by_session_and_module(
        &self,
        session_id: &str,
        module_type: ModuleType,
    ) -> Result<Vec<ExamAnswer>> {
        info!("查找模块的所有答题记录: 会话ID={}, 模块类型={}", session_id, module_type);

        let models = ExamAnswerEntity::find()
            .filter(crate::infrastructure::database::models::exam_answer::Column::SessionId.eq(session_id))
            .filter(crate::infrastructure::database::models::exam_answer::Column::ModuleType.eq(module_type.to_string()))
            .order_by_asc(crate::infrastructure::database::models::exam_answer::Column::ModuleSequence)
            .all(&self.db)
            .await
            .map_err(|e| crate::error::Error::database(format!("查询模块答题记录失败: {}", e)))?;

        let answers: Vec<ExamAnswer> = models.into_iter()
            .map(|model| self.convert_model_to_domain(model))
            .collect();

        info!("找到 {} 条模块答题记录", answers.len());
        Ok(answers)
    }

    async fn count_answers_by_session(&self, session_id: &str) -> Result<(i32, i32, i32)> {
        info!("统计会话答题情况: 会话ID={}", session_id);

        let models = ExamAnswerEntity::find()
            .filter(crate::infrastructure::database::models::exam_answer::Column::SessionId.eq(session_id))
            .filter(crate::infrastructure::database::models::exam_answer::Column::AnswerStatus.eq("answered"))
            .all(&self.db)
            .await
            .map_err(|e| crate::error::Error::database(format!("统计答题记录失败: {}", e)))?;

        let total_answered = models.len() as i32;
        let total_correct = models.iter()
            .filter(|model| model.is_correct.unwrap_or(false))
            .count() as i32;
        let total_incorrect = total_answered - total_correct;

        info!("会话答题统计: 总数={}, 正确={}, 错误={}",
              total_answered, total_correct, total_incorrect);
        Ok((total_answered, total_correct, total_incorrect))
    }
}

impl ExamAnswerRepositoryImpl {
    /// 将数据库模型转换为领域实体
    fn convert_model_to_domain(&self, model: ExamAnswerModel) -> ExamAnswer {
        use crate::domain::exam::value_objects::AnswerStatus;

        ExamAnswer {
            id: Some(model.id),
            session_id: model.session_id,
            user_id: model.user_id,
            paper_id: model.paper_id,
            question_id: model.question_id,
            module_type: ModuleType::from(model.module_type),
            module_sequence: model.module_sequence,
            user_answer: model.user_answer,
            is_correct: model.is_correct,
            response_time_seconds: model.response_time_seconds,
            answer_status: AnswerStatus::from(model.answer_status),
            created_at: model.created_at.with_timezone(&chrono::Utc),
            updated_at: model.updated_at.with_timezone(&chrono::Utc),
        }
    }
}
