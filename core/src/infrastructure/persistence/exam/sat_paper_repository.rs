//! SAT试卷仓储实现
//!
//! 实现SAT试卷的数据访问逻辑，复用现有的t_sat_paper和t_sat_paper_question表

use async_trait::async_trait;
use sea_orm::{DatabaseConnection, EntityTrait, QueryFilter, ColumnTrait, QueryOrder};
use tracing::info;

use crate::error::Result;
use crate::domain::exam::{
    SatPaperRepository, SatPaper, SatPaperQuestion, ExamType, ModuleType
};

/// SAT试卷仓储实现
pub struct SatPaperRepositoryImpl {
    db: DatabaseConnection,
}

impl SatPaperRepositoryImpl {
    /// 创建新的SAT试卷仓储实例
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }
}

#[async_trait]
impl SatPaperRepository for SatPaperRepositoryImpl {
    async fn find_paper_by_id(&self, paper_id: i64) -> Result<Option<SatPaper>> {
        info!("查找SAT试卷: {}", paper_id);

        use crate::infrastructure::database::models::sat_paper::{Entity as SatPaperEntity, Column};
        use sea_orm::QueryOrder;

        // 根据paper_id查找version最高且status为5的版本
        let model = SatPaperEntity::find()
            .filter(Column::PaperId.eq(paper_id))
            .filter(Column::Status.eq(5)) // status为5的版本
            .order_by_desc(Column::Version) // 按version降序排列
            .one(&self.db)
            .await?;

        if let Some(model) = model {
            let paper = SatPaper {
                paper_id: model.paper_id,
                paper_name: model.paper_name,
                paper_inner_name: model.paper_inner_name,
                version: model.version,
                status: model.status,
                subject_id: model.subject_id,
            };

            info!("找到SAT试卷: {} (version: {}, status: {})", paper.paper_name, paper.version, paper.status);
            Ok(Some(paper))
        } else {
            info!("未找到SAT试卷: paper_id={}, status=5", paper_id);
            Ok(None)
        }
    }

    async fn find_questions_by_paper_and_module(
        &self,
        paper_id: i64,
        module_type: ModuleType,
    ) -> Result<Vec<SatPaperQuestion>> {
        info!("查找试卷题目: paper_id={}, module_type={}", paper_id, module_type);

        use crate::infrastructure::database::models::sat_paper_question::{Entity as SatPaperQuestionEntity, Column};
        use sea_orm::QueryOrder;

        let module_type_str = module_type.to_string();

        let models = SatPaperQuestionEntity::find()
            .filter(Column::PaperId.eq(paper_id))
            .filter(Column::ModuleType.eq(&module_type_str))
            .order_by_asc(Column::ModuleSequence)
            .all(&self.db)
            .await?;

        let questions: Vec<SatPaperQuestion> = models
            .into_iter()
            .map(|model| SatPaperQuestion {
                paper_id: model.paper_id,
                question_id: model.question_id,
                module_type: model.module_type,
                module_sequence: model.module_sequence,
                subject_id: model.subject_id,
                subject_group_id: model.subject_group_id,
            })
            .collect();

        info!("找到 {} 道题目", questions.len());
        Ok(questions)
    }

    async fn find_all_questions_by_paper(&self, paper_id: i64) -> Result<Vec<SatPaperQuestion>> {
        info!("查找试卷所有题目: paper_id={}", paper_id);

        use crate::infrastructure::database::models::sat_paper_question::{Entity as SatPaperQuestionEntity, Column};
        use sea_orm::QueryOrder;

        let models = SatPaperQuestionEntity::find()
            .filter(Column::PaperId.eq(paper_id))
            .order_by_asc(Column::SubjectId)
            .order_by_asc(Column::ModuleType)
            .order_by_asc(Column::ModuleSequence)
            .all(&self.db)
            .await?;

        let questions: Vec<SatPaperQuestion> = models
            .into_iter()
            .map(|model| SatPaperQuestion {
                paper_id: model.paper_id,
                question_id: model.question_id,
                module_type: model.module_type,
                module_sequence: model.module_sequence,
                subject_id: model.subject_id,
                subject_group_id: model.subject_group_id,
            })
            .collect();

        info!("找到 {} 道题目", questions.len());
        Ok(questions)
    }

    async fn find_questions_by_paper_and_exam_type(
        &self,
        paper_id: i64,
        exam_type: ExamType,
    ) -> Result<Vec<SatPaperQuestion>> {
        info!("查找试卷题目: paper_id={}, exam_type={}", paper_id, exam_type);

        use crate::infrastructure::database::models::sat_paper_question::{Entity as SatPaperQuestionEntity, Column};
        use sea_orm::QueryOrder;

        let mut query = SatPaperQuestionEntity::find()
            .filter(Column::PaperId.eq(paper_id));

        // 根据考试类型过滤学科
        query = match exam_type {
            ExamType::Full => {
                // 全长考试：包含所有题目
                query
                    .order_by_asc(Column::SubjectId)
                    .order_by_asc(Column::ModuleType)
                    .order_by_asc(Column::ModuleSequence)
            }
            ExamType::Math => {
                // 仅数学：subject_id = 1
                query
                    .filter(Column::SubjectId.eq(1))
                    .order_by_asc(Column::ModuleType)
                    .order_by_asc(Column::ModuleSequence)
            }
            ExamType::Reading => {
                // 仅语言：subject_id = 14
                query
                    .filter(Column::SubjectId.eq(14))
                    .order_by_asc(Column::ModuleType)
                    .order_by_asc(Column::ModuleSequence)
            }
        };

        let models = query.all(&self.db).await?;

        let questions: Vec<SatPaperQuestion> = models
            .into_iter()
            .map(|model| SatPaperQuestion {
                paper_id: model.paper_id,
                question_id: model.question_id,
                module_type: model.module_type,
                module_sequence: model.module_sequence,
                subject_id: model.subject_id,
                subject_group_id: model.subject_group_id,
            })
            .collect();

        info!("找到 {} 道题目", questions.len());
        Ok(questions)
    }

    async fn find_question_contents_by_paper(
        &self,
        paper_id: i64,
        include_answers: bool,
    ) -> Result<Vec<crate::domain::exam::repository::QuestionContentDetail>> {
        info!("查找试卷题目内容详情: paper_id={}, include_answers={}", paper_id, include_answers);

        use crate::infrastructure::database::models::sat_paper_question::{Entity as SatPaperQuestionEntity, Column as PaperQuestionColumn};
        use crate::infrastructure::database::models::question::{Entity as QuestionEntity, Column as QuestionColumn};

        // 使用两步查询避免 SeaORM JOIN 的表名冲突问题
        // 第一步：获取试卷题目关联信息
        let paper_questions = SatPaperQuestionEntity::find()
            .filter(PaperQuestionColumn::PaperId.eq(paper_id))
            .order_by_asc(PaperQuestionColumn::SubjectId)
            .order_by_asc(PaperQuestionColumn::ModuleType)
            .order_by_asc(PaperQuestionColumn::ModuleSequence)
            .all(&self.db)
            .await?;

        if paper_questions.is_empty() {
            info!("试卷 {} 没有关联的题目", paper_id);
            return Ok(Vec::new());
        }

        // 第二步：批量查询题目详情
        let question_ids: Vec<i32> = paper_questions.iter().map(|pq| pq.question_id).collect();
        let questions = QuestionEntity::find()
            .filter(QuestionColumn::QuestionId.is_in(question_ids))
            .all(&self.db)
            .await?;

        // 创建题目ID到题目的映射
        let question_map: std::collections::HashMap<i32, _> = questions
            .into_iter()
            .map(|q| (q.question_id, q))
            .collect();

        // 第三步：按试卷顺序构建结果
        let mut question_details = Vec::new();

        for paper_question in paper_questions {
            if let Some(question) = question_map.get(&paper_question.question_id) {
                let detail = crate::domain::exam::repository::QuestionContentDetail {
                    question_id: question.question_id,
                    subject_id: question.subject_id,
                    knowledge_id: question.knowledge_id,
                    type_id: question.type_id,
                    difficulty: question.difficulty,
                    question_content: question.question_content.clone(),
                    options: question.options.clone(),
                    answer: if include_answers { question.answer.clone() } else { serde_json::Value::Null },
                    explanation: if include_answers { question.explanation.clone() } else { None },
                    elo_rating: question.elo_rating,
                    irt_difficulty: question.irt_difficulty,
                    irt_discrimination: question.irt_discrimination,
                    irt_guessing: question.irt_guessing,
                    usage_count: question.usage_count,
                    correct_count: question.correct_count,
                    question_set: question.question_set.clone(),
                    url: question.url.clone(),
                    is_active: question.is_active,
                    created_at: question.created_at.with_timezone(&chrono::Utc),
                    updated_at: question.updated_at.with_timezone(&chrono::Utc),
                    section_id: question.section_id,
                    status: Some(question.status), // 状态字段
                    // 试卷相关信息
                    module_type: paper_question.module_type,
                    module_sequence: paper_question.module_sequence,
                    subject_group_id: paper_question.subject_group_id,
                };

                question_details.push(detail);
            }
        }

        info!("找到试卷题目内容详情: paper_id={}, 题目数量={}", paper_id, question_details.len());
        Ok(question_details)
    }
}
