//! 考试答题服务实现
//!
//! 负责处理答题提交、答题记录等功能

use std::sync::Arc;
use tracing::info;

use crate::error::Result;
use crate::domain::exam::{
    ExamAnswerRepository, ExamSessionRepository, ExamModuleProgressRepository, SatPaperRepository,
    SessionStatus, Subject, ModuleStatus, ExamAnswer, AnswerStatus,
};

use super::super::dto::{
    SubmitAnswerRequestDto, SubmitAnswerResponseDto,
    ModuleProgressDto,
};

/// 答题服务实现
pub struct AnswerServiceImpl {
    answer_repository: Arc<dyn ExamAnswerRepository>,
    session_repository: Arc<dyn ExamSessionRepository>,
    progress_repository: Arc<dyn ExamModuleProgressRepository>,
    paper_repository: Arc<dyn SatPaperRepository>,
}

impl AnswerServiceImpl {
    pub fn new(
        answer_repository: Arc<dyn ExamAnswerRepository>,
        session_repository: Arc<dyn ExamSessionRepository>,
        progress_repository: Arc<dyn ExamModuleProgressRepository>,
        paper_repository: Arc<dyn SatPaperRepository>,
    ) -> Self {
        Self {
            answer_repository,
            session_repository,
            progress_repository,
            paper_repository,
        }
    }

    /// 提交答案
    pub async fn submit_answer(
        &self,
        request: SubmitAnswerRequestDto,
    ) -> Result<SubmitAnswerResponseDto> {
        info!("处理答题提交: 会话ID={}, 题目ID={}", request.session_id, request.question_id);

        // 1. 验证会话有效性并获取会话信息
        let session = self.session_repository
            .find_by_session_id(&request.session_id)
            .await?
            .ok_or_else(|| crate::error::Error::service("会话不存在"))?;

        if session.user_id != request.user_id {
            return Err(crate::error::Error::service("用户无权访问此会话"));
        }

        if session.session_status != SessionStatus::InProgress {
            return Err(crate::error::Error::service("会话已结束"));
        }

        // 2. 获取题目信息和正确答案
        let question_details = self.paper_repository
            .find_question_contents_by_paper(session.paper_id, true) // 需要答案来判断正确性
            .await?;

        let question_detail = question_details
            .iter()
            .find(|q| q.question_id == request.question_id)
            .ok_or_else(|| crate::error::Error::service("题目不存在"))?;

        // 3. 判断答案正确性
        let correct_answer_str = question_detail.answer.as_str()
            .unwrap_or("A")
            .to_string();
        let is_correct = request.student_answer.trim().to_uppercase() == correct_answer_str.to_uppercase();

        // 4. 创建答题记录
        let answer_record = ExamAnswer {
            id: None,
            session_id: request.session_id.clone(),
            user_id: request.user_id,
            paper_id: session.paper_id,
            question_id: request.question_id,
            module_type: request.module_type,
            module_sequence: request.question_sequence,
            user_answer: Some(request.student_answer.clone()),
            is_correct: Some(is_correct),
            response_time_seconds: Some(request.time_spent_seconds),
            answer_status: AnswerStatus::Answered,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };

        // 5. 保存答题记录
        self.answer_repository.create(&answer_record).await?;

        // 6. 获取模块进度信息
        let module_progress = self.get_module_progress(&request.session_id, request.module_type).await?;

        // 7. 构建响应
        let response = SubmitAnswerResponseDto {
            success: true,
            is_correct,
            correct_answer: correct_answer_str,
            explanation: if is_correct {
                None
            } else {
                question_detail.explanation.as_ref().and_then(|e| e.as_str()).map(|s| s.to_string())
            },
            module_progress,
        };

        info!("答题提交完成: 会话ID={}, 题目ID={}, 正确={}", 
              request.session_id, request.question_id, is_correct);
        Ok(response)
    }

    /// 获取模块进度信息
    async fn get_module_progress(
        &self,
        session_id: &str,
        module_type: crate::domain::exam::ModuleType,
    ) -> Result<ModuleProgressDto> {
        // 获取模块进度记录
        let progress = self.progress_repository
            .find_by_session_and_module(session_id, module_type)
            .await?;

        // 获取该模块的答题统计
        let answers = self.answer_repository
            .find_by_session_and_module(session_id, module_type)
            .await?;

        let answered_count = answers.len() as i32;
        let correct_count = answers.iter().filter(|a| a.is_correct == Some(true)).count() as i32;

        // 确定学科信息
        let (subject, subject_name) = match module_type {
            crate::domain::exam::ModuleType::Module1 => {
                // 根据题目确定学科，这里简化处理
                if answers.is_empty() {
                    (Subject::Math, "数学".to_string())
                } else {
                    match answers[0].question_id % 2 { // 简化的学科判断
                        0 => (Subject::Math, "数学".to_string()),
                        _ => (Subject::Reading, "语言".to_string()),
                    }
                }
            },
            _ => (Subject::Math, "数学".to_string()), // 简化处理
        };

        let total_questions = module_type.question_count(subject);
        let time_limit_seconds = module_type.time_limit_minutes(subject) * 60;

        Ok(ModuleProgressDto {
            module_type,
            subject,
            subject_name,
            total_questions,
            answered_questions: answered_count,
            correct_questions: correct_count,
            time_limit_seconds,
            time_used_seconds: progress.as_ref().map(|p| p.time_used_seconds).unwrap_or(0),
            remaining_time_seconds: progress.as_ref().and_then(|p| p.remaining_time_seconds),
            module_status: progress.as_ref().map(|p| p.module_status).unwrap_or(ModuleStatus::InProgress),
            started_at: progress.as_ref().and_then(|p| p.started_at),
            completed_at: progress.as_ref().and_then(|p| p.completed_at),
        })
    }
}
