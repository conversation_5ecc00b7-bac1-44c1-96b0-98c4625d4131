//! 考试指引服务实现
//!
//! 负责处理考试指引信息生成、会话创建等功能

use tracing::info;
use uuid::Uuid;

use crate::error::Result;
use crate::domain::exam::{
    ExamSessionRepository, SatPaperRepository,
    ExamSession, SessionStatus, ExamType, Subject, ModuleType,
};

use super::super::dto::{
    ExamGuideRequestDto, ExamGuideResponseDto,
    MockExamSectionInfoDto, ExamInstructionsDto,
};

/// 考试指引服务实现
pub struct GuideServiceImpl;

impl GuideServiceImpl {
    pub fn new() -> Self {
        Self
    }

    /// 获取考试指引
    pub async fn get_exam_guide(
        &self,
        request: ExamGuideRequestDto,
        session_repository: &dyn ExamSessionRepository,
        paper_repository: &dyn SatPaperRepository,
    ) -> Result<ExamGuideResponseDto> {
        info!("获取考试指引: 用户ID={}, 考试类型={:?}, 学科={:?}, 会话ID={:?}",
              request.user_id, request.exam_type, request.subject, request.session_id);

        // 判断是整体指引还是学科指引
        if let Some(subject) = request.subject {
            // 学科特定指引
            self.get_subject_guide(request, subject, session_repository).await
        } else {
            // 整体考试指引
            self.get_overall_guide(request, session_repository, paper_repository).await
        }
    }

    /// 获取整体考试指引（创建新会话）
    async fn get_overall_guide(
        &self,
        request: ExamGuideRequestDto,
        session_repository: &dyn ExamSessionRepository,
        _paper_repository: &dyn SatPaperRepository,
    ) -> Result<ExamGuideResponseDto> {
        // 1. 生成会话ID
        let session_id = format!("mock_exam_{}_{}",
            chrono::Utc::now().format("%Y%m%d_%H%M%S"),
            Uuid::new_v4().to_string()[..8].to_uppercase()
        );

        // 2. 获取默认试卷ID
        let paper_id = self.get_default_paper_id(&request.exam_type).await?;

        // 3. 创建考试会话
        let exam_session = ExamSession {
            session_id: session_id.clone(),
            user_id: request.user_id,
            paper_id: paper_id.into(),
            exam_type: request.exam_type.clone(),
            current_subject: self.get_initial_subject(&request.exam_type),
            current_module_type: Some(ModuleType::Module1),
            session_status: SessionStatus::InProgress,
            total_time_seconds: self.get_total_time_seconds(&request.exam_type),
            math_time_seconds: self.get_math_time_seconds(&request.exam_type),
            reading_time_seconds: self.get_reading_time_seconds(&request.exam_type),
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            completed_at: None,
        };

        // 4. 保存会话到数据库
        session_repository.create(&exam_session).await?;

        // 5. 构建整体指引响应
        let response = ExamGuideResponseDto {
            session_id: session_id.clone(),
            exam_type: request.exam_type.clone(),
            total_questions: self.get_total_questions(&request.exam_type),
            total_time_minutes: self.get_total_time_minutes(&request.exam_type),
            sections_info: self.build_sections_info(&request.exam_type),
            instructions: self.build_instructions(&request.exam_type),
        };

        info!("整体考试指引生成成功: 会话ID={}, 考试类型={:?}, 总题数={}, 总时间={}分钟",
              session_id, request.exam_type, response.total_questions, response.total_time_minutes);

        Ok(response)
    }

    /// 获取学科特定指引（基于现有会话）
    async fn get_subject_guide(
        &self,
        request: ExamGuideRequestDto,
        subject: Subject,
        session_repository: &dyn ExamSessionRepository,
    ) -> Result<ExamGuideResponseDto> {
        let session_id = request.session_id.clone()
            .ok_or_else(|| crate::error::Error::service("学科指引需要提供会话ID"))?;

        // 验证会话存在
        let _session = session_repository.find_by_session_id(&session_id).await?
            .ok_or_else(|| crate::error::Error::service(&format!("会话不存在: {}", session_id)))?;

        // 构建学科特定指引响应
        let response = ExamGuideResponseDto {
            session_id: session_id.clone(),
            exam_type: request.exam_type.clone(),
            total_questions: self.get_subject_questions(&subject),
            total_time_minutes: self.get_subject_time_minutes(&subject),
            sections_info: self.build_subject_sections_info(&subject),
            instructions: self.build_subject_instructions(&subject),
        };

        info!("学科指引生成成功: 会话ID={}, 学科={:?}, 题数={}, 时间={}分钟",
              session_id, subject, response.total_questions, response.total_time_minutes);

        Ok(response)
    }

    /// 获取默认试卷ID
    async fn get_default_paper_id(&self, exam_type: &ExamType) -> Result<i32> {
        // TODO: 根据考试类型从数据库获取合适的试卷
        // 这里暂时使用固定值
        match exam_type {
            ExamType::Full => Ok(1),
            ExamType::Math => Ok(2),
            ExamType::Reading => Ok(3),
        }
    }

    /// 获取初始学科
    fn get_initial_subject(&self, exam_type: &ExamType) -> Subject {
        match exam_type {
            ExamType::Full => Subject::Reading, // 全长模考先考语言
            ExamType::Math => Subject::Math,
            ExamType::Reading => Subject::Reading,
        }
    }

    /// 获取总题数
    fn get_total_questions(&self, exam_type: &ExamType) -> i32 {
        match exam_type {
            ExamType::Full => 98,    // 语言54题 + 数学44题
            ExamType::Math => 44,    // 数学44题
            ExamType::Reading => 54, // 语言54题
        }
    }

    /// 获取总时间（分钟）
    fn get_total_time_minutes(&self, exam_type: &ExamType) -> i32 {
        match exam_type {
            ExamType::Full => 134,   // 语言64分钟 + 数学70分钟
            ExamType::Math => 70,    // 数学70分钟
            ExamType::Reading => 64, // 语言64分钟
        }
    }

    /// 获取总时间（秒）
    fn get_total_time_seconds(&self, exam_type: &ExamType) -> i32 {
        self.get_total_time_minutes(exam_type) * 60
    }

    /// 获取数学时间（秒）
    fn get_math_time_seconds(&self, exam_type: &ExamType) -> i32 {
        match exam_type {
            ExamType::Full => 70 * 60,   // 数学70分钟
            ExamType::Math => 70 * 60,   // 数学70分钟
            ExamType::Reading => 0,      // 语言模考无数学
        }
    }

    /// 获取语言时间（秒）
    fn get_reading_time_seconds(&self, exam_type: &ExamType) -> i32 {
        match exam_type {
            ExamType::Full => 64 * 60,   // 语言64分钟
            ExamType::Math => 0,         // 数学模考无语言
            ExamType::Reading => 64 * 60, // 语言64分钟
        }
    }

    /// 构建学科信息
    fn build_sections_info(&self, exam_type: &ExamType) -> Vec<MockExamSectionInfoDto> {
        match exam_type {
            ExamType::Full => vec![
                MockExamSectionInfoDto {
                    section_name: "Reading and Writing".to_string(),
                    modules: 2,
                    questions_per_module: 27,
                    time_per_module: 32,
                    instructions: "语言部分包含两个模块，第二个模块难度将根据第一个模块的表现自适应调整。每个模块32分钟，最后5分钟计时器将强制显示。".to_string(),
                },
                MockExamSectionInfoDto {
                    section_name: "Math".to_string(),
                    modules: 2,
                    questions_per_module: 22,
                    time_per_module: 35,
                    instructions: "数学部分包含两个模块，第二个模块难度将根据第一个模块的表现自适应调整。每个模块35分钟，最后5分钟计时器将强制显示。".to_string(),
                },
            ],
            ExamType::Math => vec![
                MockExamSectionInfoDto {
                    section_name: "Math".to_string(),
                    modules: 2,
                    questions_per_module: 22,
                    time_per_module: 35,
                    instructions: "数学部分包含两个模块，第二个模块难度将根据第一个模块的表现自适应调整。每个模块35分钟，最后5分钟计时器将强制显示。".to_string(),
                },
            ],
            ExamType::Reading => vec![
                MockExamSectionInfoDto {
                    section_name: "Reading and Writing".to_string(),
                    modules: 2,
                    questions_per_module: 27,
                    time_per_module: 32,
                    instructions: "语言部分包含两个模块，第二个模块难度将根据第一个模块的表现自适应调整。每个模块32分钟，最后5分钟计时器将强制显示。".to_string(),
                },
            ],
        }
    }

    /// 构建考试说明
    fn build_instructions(&self, exam_type: &ExamType) -> ExamInstructionsDto {
        match exam_type {
            ExamType::Full => ExamInstructionsDto {
                full_exam_note: Some("全长模考按照真实考试顺序：先语言后数学".to_string()),
                break_info: Some("语言部分完成后有10分钟休息时间".to_string()),
                timer_note: "考试过程中请注意时间管理".to_string(),
            },
            ExamType::Math => ExamInstructionsDto {
                full_exam_note: None,
                break_info: None,
                timer_note: "考试过程中请注意时间管理".to_string(),
            },
            ExamType::Reading => ExamInstructionsDto {
                full_exam_note: None,
                break_info: None,
                timer_note: "考试过程中请注意时间管理".to_string(),
            },
        }
    }

    /// 获取学科题数
    fn get_subject_questions(&self, subject: &Subject) -> i32 {
        match subject {
            Subject::Math => 44,    // 数学44题
            Subject::Reading => 54, // 语言54题
        }
    }

    /// 获取学科时间（分钟）
    fn get_subject_time_minutes(&self, subject: &Subject) -> i32 {
        match subject {
            Subject::Math => 70,    // 数学70分钟
            Subject::Reading => 64, // 语言64分钟
        }
    }

    /// 构建学科特定的学科信息
    fn build_subject_sections_info(&self, subject: &Subject) -> Vec<MockExamSectionInfoDto> {
        match subject {
            Subject::Math => vec![
                MockExamSectionInfoDto {
                    section_name: "Math".to_string(),
                    modules: 2,
                    questions_per_module: 22,
                    time_per_module: 35,
                    instructions: "数学部分包含两个模块，第二个模块难度将根据第一个模块的表现自适应调整。每个模块35分钟，最后5分钟计时器将强制显示。".to_string(),
                },
            ],
            Subject::Reading => vec![
                MockExamSectionInfoDto {
                    section_name: "Reading and Writing".to_string(),
                    modules: 2,
                    questions_per_module: 27,
                    time_per_module: 32,
                    instructions: "语言部分包含两个模块，第二个模块难度将根据第一个模块的表现自适应调整。每个模块32分钟，最后5分钟计时器将强制显示。".to_string(),
                },
            ],
        }
    }

    /// 构建学科特定的考试说明
    fn build_subject_instructions(&self, subject: &Subject) -> ExamInstructionsDto {
        match subject {
            Subject::Math => ExamInstructionsDto {
                full_exam_note: Some("数学部分包含两个模块，第二个模块难度将根据第一个模块的表现自适应调整".to_string()),
                break_info: None,
                timer_note: "每个模块35分钟，最后5分钟计时器将强制显示".to_string(),
            },
            Subject::Reading => ExamInstructionsDto {
                full_exam_note: Some("语言部分包含两个模块，第二个模块难度将根据第一个模块的表现自适应调整".to_string()),
                break_info: Some("完成语言部分后将有10分钟休息时间".to_string()),
                timer_note: "每个模块32分钟，最后5分钟计时器将强制显示".to_string(),
            },
        }
    }
}
