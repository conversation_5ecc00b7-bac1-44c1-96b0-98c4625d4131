//! 模块提交服务实现
//!
//! 负责处理考试模块的提交、成绩计算、自适应逻辑等功能

use std::sync::Arc;
use tracing::{info, warn};

use crate::error::Result;
use crate::domain::exam::{
    ExamSession, ExamAnswer, ExamModuleProgress, SatPaperQuestion,
    ExamSessionRepository, ExamAnswerRepository, ExamModuleProgressRepository, SatPaperRepository,
    AdaptiveModuleService, ModuleType, Subject, ModuleStatus, SessionStatus,
};

use super::super::dto::{
    SubmitModuleRequestDto, SubmitModuleResponseDto,
    ModuleScoreDto, ModuleStatisticsDto, NextActionDto, AdaptiveResultDto,
    NextActionType, ScoreRangeDto, ThresholdInfoDto, NextModuleInfoDto,
};

use super::data_consistency_service::DataConsistencyService;

/// 模块提交服务实现
pub struct ModuleSubmitServiceImpl {
    session_repository: Arc<dyn ExamSessionRepository>,
    answer_repository: Arc<dyn ExamAnswerRepository>,
    progress_repository: Arc<dyn ExamModuleProgressRepository>,
    paper_repository: Arc<dyn SatPaperRepository>,
    adaptive_service: Arc<AdaptiveModuleService>,
    consistency_service: DataConsistencyService,
}

impl ModuleSubmitServiceImpl {
    pub fn new(
        session_repository: Arc<dyn ExamSessionRepository>,
        answer_repository: Arc<dyn ExamAnswerRepository>,
        progress_repository: Arc<dyn ExamModuleProgressRepository>,
        paper_repository: Arc<dyn SatPaperRepository>,
        adaptive_service: Arc<AdaptiveModuleService>,
    ) -> Self {
        let consistency_service = DataConsistencyService::new(
            session_repository.clone(),
            answer_repository.clone(),
            progress_repository.clone(),
            paper_repository.clone(),
        );

        Self {
            session_repository,
            answer_repository,
            progress_repository,
            paper_repository,
            adaptive_service,
            consistency_service,
        }
    }

    /// 提交模块
    pub async fn submit_module(
        &self,
        request: SubmitModuleRequestDto,
    ) -> Result<SubmitModuleResponseDto> {
        info!("开始提交模块: 会话ID={}, 模块类型={}, 用户ID={}, 强制提交={:?}", 
              request.session_id, request.module_type, request.user_id, request.force_submit);

        // 1. 验证会话和权限
        let mut session = self.validate_session_and_permission(&request).await?;

        // 2. 获取模块进度
        let mut module_progress = self.get_module_progress(&request).await?;

        // 3. 验证模块可以提交
        self.validate_module_can_submit(&module_progress, request.force_submit.unwrap_or(false))?;

        // 4. 获取模块答题记录
        let answers = self.get_module_answers(&request).await
            .map_err(|e| {
                warn!("获取模块答题记录失败: {}", e);
                crate::error::Error::service("获取答题记录失败，请稍后重试")
            })?;

        // 5. 获取模块题目信息
        let questions = self.get_module_questions(&session, request.module_type).await
            .map_err(|e| {
                warn!("获取模块题目信息失败: {}", e);
                crate::error::Error::service("获取题目信息失败，请稍后重试")
            })?;

        // 6. 验证数据一致性
        self.validate_data_consistency(&answers, &questions, &module_progress)?;

        // 6.5. 执行全面的数据一致性检查（可选，用于调试）
        if std::env::var("ENABLE_CONSISTENCY_CHECK").unwrap_or_default() == "true" {
            match self.consistency_service.check_exam_consistency(&request.session_id, request.user_id).await {
                Ok(report) => {
                    if !report.is_all_passed() {
                        warn!("数据一致性检查发现问题: 通过率={:.1}%, 详情={:?}",
                              report.success_rate() * 100.0, report.checks);
                    } else {
                        info!("数据一致性检查通过: 检查项={}", report.total_checks);
                    }
                }
                Err(e) => {
                    warn!("数据一致性检查失败: {}", e);
                    // 不阻止模块提交，只记录警告
                }
            }
        }

        // 7. 更新基础统计（不包含时间管理）
        let mut updated_progress = self.update_basic_statistics(&mut module_progress, &answers).await?;

        // 8. 计算模块成绩
        let module_score = self.calculate_module_score(&answers, &questions, request.module_type, session.current_subject).await
            .map_err(|e| {
                warn!("计算模块成绩失败: {}", e);
                crate::error::Error::service("成绩计算失败，请稍后重试")
            })?;

        // 9. 计算模块统计
        let module_statistics = self.calculate_module_statistics(&answers, &updated_progress).await
            .map_err(|e| {
                warn!("计算模块统计失败: {}", e);
                crate::error::Error::service("统计计算失败，请稍后重试")
            })?;

        // 10. 更新模块状态
        updated_progress.submit();
        self.progress_repository.update(&updated_progress).await
            .map_err(|e| {
                warn!("更新模块状态失败: {}", e);
                crate::error::Error::service("保存模块状态失败，请稍后重试")
            })?;

        // 11. 处理自适应逻辑（如果是第一模块）
        let adaptive_info = if request.module_type == ModuleType::Module1 {
            match self.handle_adaptive_logic(&request, &module_score).await {
                Ok(info) => Some(info),
                Err(e) => {
                    warn!("自适应逻辑处理失败: {}", e);
                    // 自适应逻辑失败不应该阻止模块提交，但需要记录错误
                    return Err(crate::error::Error::service("自适应逻辑处理失败，请联系管理员"));
                }
            }
        } else {
            None
        };

        // 12. 确定下一步操作和下一个模块信息
        let (next_action, next_module_info) = self.determine_next_action_and_module(&session, request.module_type, &adaptive_info).await
            .map_err(|e| {
                warn!("确定下一步操作失败: {}", e);
                crate::error::Error::service("确定下一步操作失败")
            })?;

        // 13. 更新会话状态（如果需要）
        self.update_session_if_needed(&mut session, request.module_type, &next_action, &next_module_info).await
            .map_err(|e| {
                warn!("更新会话状态失败: {}", e);
                crate::error::Error::service("更新会话状态失败，请稍后重试")
            })?;

        let response = SubmitModuleResponseDto {
            success: true,
            module_score,
            module_statistics,
            next_action,
            adaptive_info,
            next_module_info,
        };

        info!("模块提交成功: 会话ID={}, 模块类型={}, 原始分数={}/{}, 正确率={:.1}%",
              request.session_id, request.module_type, 
              response.module_score.raw_score, response.module_score.max_score,
              response.module_score.accuracy_rate * 100.0);

        Ok(response)
    }

    /// 验证会话和权限
    async fn validate_session_and_permission(
        &self,
        request: &SubmitModuleRequestDto,
    ) -> Result<ExamSession> {
        let session = self.session_repository
            .find_by_session_id(&request.session_id)
            .await?
            .ok_or_else(|| crate::error::Error::service("会话不存在"))?;

        if session.user_id != request.user_id {
            return Err(crate::error::Error::service("用户无权访问此会话"));
        }

        if session.session_status != SessionStatus::InProgress {
            return Err(crate::error::Error::service("会话状态不允许提交模块"));
        }

        Ok(session)
    }

    /// 获取模块进度
    async fn get_module_progress(
        &self,
        request: &SubmitModuleRequestDto,
    ) -> Result<ExamModuleProgress> {
        self.progress_repository
            .find_by_session_and_module(&request.session_id, request.module_type)
            .await?
            .ok_or_else(|| crate::error::Error::service("模块进度不存在，请先开始模块"))
    }

    /// 验证模块可以提交
    fn validate_module_can_submit(
        &self,
        progress: &ExamModuleProgress,
        force_submit: bool,
    ) -> Result<()> {
        match progress.module_status {
            ModuleStatus::NotStarted => {
                return Err(crate::error::Error::invalid_input(
                    "模块尚未开始，无法提交。请先开始模块。"
                ));
            }
            ModuleStatus::Submitted => {
                return Err(crate::error::Error::invalid_input(
                    "模块已经提交，无法重复提交。如需重新提交，请联系管理员。"
                ));
            }
            ModuleStatus::Completed => {
                return Err(crate::error::Error::invalid_input(
                    "模块已经完成，无法重复提交。"
                ));
            }
            ModuleStatus::InProgress => {
                // 检查是否完成所有题目
                if !force_submit && progress.answered_questions < progress.total_questions {
                    let missing_questions = progress.total_questions - progress.answered_questions;
                    warn!("模块未完成所有题目: 已答={}, 总数={}, 缺少={}",
                          progress.answered_questions, progress.total_questions, missing_questions);

                    return Err(crate::error::Error::invalid_input(
                        &format!("模块还有{}道题目未完成。如需强制提交，请设置force_submit=true。", missing_questions)
                    ));
                }

                // 时间管理已关闭，不进行时间相关验证
                info!("时间管理功能已关闭，跳过时间验证");
            }
            ModuleStatus::Available | ModuleStatus::Pending => {
                return Err(crate::error::Error::invalid_input(
                    "模块状态异常，无法提交。请重新开始模块。"
                ));
            }
        }
        Ok(())
    }

    /// 获取模块答题记录
    async fn get_module_answers(
        &self,
        request: &SubmitModuleRequestDto,
    ) -> Result<Vec<ExamAnswer>> {
        self.answer_repository
            .find_by_session_and_module(&request.session_id, request.module_type)
            .await
    }

    /// 获取模块题目信息
    async fn get_module_questions(
        &self,
        session: &ExamSession,
        module_type: ModuleType,
    ) -> Result<Vec<SatPaperQuestion>> {
        self.paper_repository
            .find_questions_by_paper_and_module(session.paper_id, module_type)
            .await
    }

    /// 计算模块成绩
    async fn calculate_module_score(
        &self,
        answers: &[ExamAnswer],
        questions: &[SatPaperQuestion],
        module_type: ModuleType,
        subject: Subject,
    ) -> Result<ModuleScoreDto> {
        let total_questions = questions.len() as i32;
        let correct_answers = answers.iter()
            .filter(|a| a.is_correct == Some(true))
            .count() as i32;

        let accuracy_rate = if total_questions > 0 {
            correct_answers as f64 / total_questions as f64
        } else {
            0.0
        };

        // 计算SAT分数（简化版本）
        let sat_score = self.calculate_sat_module_score(correct_answers, total_questions, subject);

        Ok(ModuleScoreDto {
            module_type,
            subject,
            subject_name: self.get_subject_name(subject),
            raw_score: correct_answers,
            max_score: total_questions,
            accuracy_rate,
            sat_score: Some(sat_score),
            score_range: Some(self.calculate_score_range(sat_score)),
        })
    }

    /// 计算模块统计
    async fn calculate_module_statistics(
        &self,
        answers: &[ExamAnswer],
        progress: &ExamModuleProgress,
    ) -> Result<ModuleStatisticsDto> {
        let answered_questions = answers.len() as i32;
        let correct_questions = answers.iter()
            .filter(|a| a.is_correct == Some(true))
            .count() as i32;
        let skipped_questions = progress.total_questions - answered_questions;

        let total_time_seconds = answers.iter()
            .map(|a| a.response_time_seconds.unwrap_or(0))
            .sum::<i32>();

        let average_time_per_question = if answered_questions > 0 {
            total_time_seconds as f64 / answered_questions as f64
        } else {
            0.0
        };

        let completion_rate = answered_questions as f64 / progress.total_questions as f64;
        let accuracy_rate = if answered_questions > 0 {
            correct_questions as f64 / answered_questions as f64
        } else {
            0.0
        };

        Ok(ModuleStatisticsDto {
            total_questions: progress.total_questions,
            answered_questions,
            correct_questions,
            skipped_questions,
            average_time_per_question,
            total_time_seconds,
            remaining_time_seconds: progress.remaining_time_seconds.unwrap_or(0),
            completion_rate,
            accuracy_rate,
        })
    }

    /// 处理自适应逻辑
    async fn handle_adaptive_logic(
        &self,
        request: &SubmitModuleRequestDto,
        module_score: &ModuleScoreDto,
    ) -> Result<AdaptiveResultDto> {
        info!("处理第一模块自适应逻辑: 会话ID={}, 正确率={:.2}%", 
              request.session_id, module_score.accuracy_rate * 100.0);

        // 获取当前会话的学科
        let session = self.session_repository
            .find_by_session_id(&request.session_id)
            .await?
            .ok_or_else(|| crate::error::Error::service("会话不存在"))?;

        // 使用自适应服务确定第二模块类型
        let module2_type = self.adaptive_service
            .evaluate_and_create_module2(&request.session_id, session.current_subject)
            .await?;

        // 获取阈值信息
        let threshold = self.adaptive_service.get_adaptive_threshold(session.current_subject);
        let performance_score = module_score.accuracy_rate * 100.0;
        let threshold_met = performance_score >= threshold as f64;

        let adaptive_reason = if threshold_met {
            format!("第一模块正确率{:.1}%达到阈值{:.1}%，进入困难模块", performance_score, threshold)
        } else {
            format!("第一模块正确率{:.1}%未达到阈值{:.1}%，进入简单模块", performance_score, threshold)
        };

        Ok(AdaptiveResultDto {
            module1_performance_score: performance_score,
            module2_type,
            adaptive_reason,
            threshold_info: ThresholdInfoDto {
                threshold: threshold as f64,
                actual_score: performance_score,
                threshold_met,
            },
        })
    }

    /// 确定下一步操作
    async fn determine_next_action(
        &self,
        session: &ExamSession,
        submitted_module_type: ModuleType,
        _adaptive_info: &Option<AdaptiveResultDto>,
    ) -> Result<NextActionDto> {
        match submitted_module_type {
            ModuleType::Module1 => {
                // 第一模块完成，开始第二模块
                Ok(NextActionDto {
                    action_type: NextActionType::StartModule2,
                    description: "第一模块已完成，可以开始第二模块".to_string(),
                    can_continue: true,
                    suggested_wait_seconds: None,
                })
            }
            ModuleType::Module2E | ModuleType::Module2H => {
                // 第二模块完成，根据考试类型决定下一步
                match session.exam_type {
                    crate::domain::exam::ExamType::Full => {
                        // 全长考试，检查是否需要切换学科
                        match session.current_subject {
                            Subject::Reading => {
                                // 阅读部分完成，需要切换到数学部分
                                Ok(NextActionDto {
                                    action_type: NextActionType::SwitchSubject,
                                    description: "语言部分已完成，休息10分钟后开始数学部分".to_string(),
                                    can_continue: true,
                                    suggested_wait_seconds: Some(600), // 10分钟
                                })
                            }
                            Subject::Math => {
                                // 数学部分完成，整个考试结束
                                Ok(NextActionDto {
                                    action_type: NextActionType::CompleteExam,
                                    description: "数学部分已完成，可以提交整个考试".to_string(),
                                    can_continue: true,
                                    suggested_wait_seconds: None,
                                })
                            }
                        }
                    }
                    _ => {
                        // 单科考试，直接完成
                        Ok(NextActionDto {
                            action_type: NextActionType::CompleteExam,
                            description: "考试已完成，可以提交整个考试".to_string(),
                            can_continue: true,
                            suggested_wait_seconds: None,
                        })
                    }
                }
            }
        }
    }

    /// 确定下一步操作和下一个模块信息
    async fn determine_next_action_and_module(
        &self,
        session: &ExamSession,
        submitted_module_type: ModuleType,
        adaptive_info: &Option<AdaptiveResultDto>,
    ) -> Result<(NextActionDto, Option<NextModuleInfoDto>)> {
        match submitted_module_type {
            ModuleType::Module1 => {
                // 第一模块完成，开始第二模块
                let next_action = NextActionDto {
                    action_type: NextActionType::StartModule2,
                    description: "第一模块已完成，可以开始第二模块".to_string(),
                    can_continue: true,
                    suggested_wait_seconds: None,
                };

                // 根据自适应结果确定第二模块类型
                let next_module_type = if let Some(adaptive) = adaptive_info {
                    if adaptive.threshold_info.threshold_met {
                        ModuleType::Module2H // 困难版本
                    } else {
                        ModuleType::Module2E // 简单版本
                    }
                } else {
                    ModuleType::Module2E // 默认简单版本
                };

                let next_module_info = Some(self.build_next_module_info(
                    next_module_type,
                    session.current_subject,
                    true,
                    None,
                ));

                Ok((next_action, next_module_info))
            }
            ModuleType::Module2E | ModuleType::Module2H => {
                // 第二模块完成，根据考试类型决定下一步
                match session.exam_type {
                    crate::domain::exam::ExamType::Full => {
                        // 全长考试，检查是否需要切换学科
                        match session.current_subject {
                            Subject::Reading => {
                                // 阅读部分完成，需要切换到数学部分
                                let next_action = NextActionDto {
                                    action_type: NextActionType::SwitchSubject,
                                    description: "语言部分已完成，休息10分钟后开始数学部分".to_string(),
                                    can_continue: true,
                                    suggested_wait_seconds: Some(600), // 10分钟
                                };

                                let next_module_info = Some(self.build_next_module_info(
                                    ModuleType::Module1,
                                    Subject::Math,
                                    false,
                                    Some(600),
                                ));

                                Ok((next_action, next_module_info))
                            }
                            Subject::Math => {
                                // 数学部分完成，整个考试结束
                                let next_action = NextActionDto {
                                    action_type: NextActionType::CompleteExam,
                                    description: "数学部分已完成，可以提交整个考试".to_string(),
                                    can_continue: true,
                                    suggested_wait_seconds: None,
                                };

                                Ok((next_action, None))
                            }
                        }
                    }
                    _ => {
                        // 单科考试，直接完成
                        let next_action = NextActionDto {
                            action_type: NextActionType::CompleteExam,
                            description: "考试已完成，可以提交整个考试".to_string(),
                            can_continue: true,
                            suggested_wait_seconds: None,
                        };

                        Ok((next_action, None))
                    }
                }
            }
        }
    }

    /// 构建下一个模块信息
    fn build_next_module_info(
        &self,
        module_type: ModuleType,
        subject: Subject,
        can_start_immediately: bool,
        suggested_wait_seconds: Option<i32>,
    ) -> NextModuleInfoDto {
        let (subject_name, question_count, time_limit_minutes) = match subject {
            Subject::Reading => ("Reading and Writing".to_string(), 27, 32),
            Subject::Math => ("Math".to_string(), 22, 35),
        };

        let difficulty_level = match module_type {
            ModuleType::Module1 => "标准难度".to_string(),
            ModuleType::Module2E => "简单版本".to_string(),
            ModuleType::Module2H => "困难版本".to_string(),
        };

        let description = match module_type {
            ModuleType::Module1 => format!("{}第一模块，标准难度", subject_name),
            ModuleType::Module2E => format!("{}第二模块，根据第一模块表现调整为简单版本", subject_name),
            ModuleType::Module2H => format!("{}第二模块，根据第一模块表现调整为困难版本", subject_name),
        };

        NextModuleInfoDto {
            module_type,
            subject,
            subject_name,
            question_count,
            time_limit_minutes,
            difficulty_level,
            description,
            can_start_immediately,
            suggested_wait_seconds,
        }
    }

    /// 更新会话状态（如果需要）
    async fn update_session_if_needed(
        &self,
        session: &mut ExamSession,
        submitted_module_type: ModuleType,
        next_action: &NextActionDto,
        next_module_info: &Option<NextModuleInfoDto>,
    ) -> Result<()> {
        let mut needs_update = false;

        // 1. 根据下一步操作更新会话状态
        match next_action.action_type {
            NextActionType::StartModule2 => {
                // 第一模块完成，准备开始第二模块
                if let Some(next_module) = next_module_info {
                    session.current_module_type = Some(next_module.module_type.clone());
                    needs_update = true;
                }
            }
            NextActionType::SwitchSubject => {
                // 切换学科，重置为第一模块
                session.switch_to_next_subject();
                session.current_module_type = Some(ModuleType::Module1);
                needs_update = true;
            }
            NextActionType::CompleteExam => {
                // 考试完成，清空当前模块
                session.current_module_type = None;
                needs_update = true;
                // 注意：这里不直接完成考试，等待用户明确提交整个考试
                // session.complete();
            }
            NextActionType::TakeBreak | NextActionType::WaitForScoring => {
                // 这些状态暂时不需要更新 session 的 current_module_type
                // 保持当前状态不变
            }
        }

        // 2. 保存更新
        if needs_update {
            info!("更新会话状态: 会话ID={}, 当前学科={:?}, 当前模块={:?}",
                  session.session_id, session.current_subject, session.current_module_type);
            self.session_repository.update(session).await?;
        }

        Ok(())
    }

    /// 更新基础统计（不包含时间管理）
    async fn update_basic_statistics(
        &self,
        progress: &mut ExamModuleProgress,
        answers: &[ExamAnswer],
    ) -> Result<ExamModuleProgress> {
        // 1. 计算答题统计
        let answered_count = answers.len() as i32;
        let correct_count = answers.iter()
            .filter(|a| a.is_correct == Some(true))
            .count() as i32;

        // 2. 计算总用时（仅用于统计，不做时间验证）
        let total_time_used = answers.iter()
            .map(|a| a.response_time_seconds.unwrap_or(0))
            .sum::<i32>();

        // 3. 更新进度统计（时间管理已关闭）
        progress.update_progress(answered_count, correct_count, total_time_used);

        info!("基础统计更新完成: 答题数={}, 正确数={}, 总用时={}秒（仅统计用）",
              answered_count, correct_count, total_time_used);

        Ok(progress.clone())
    }

    /// 验证数据一致性
    fn validate_data_consistency(
        &self,
        answers: &[ExamAnswer],
        questions: &[SatPaperQuestion],
        progress: &ExamModuleProgress,
    ) -> Result<()> {
        // 1. 验证题目数量一致性
        if questions.len() as i32 != progress.total_questions {
            warn!("题目数量不一致: 实际题目={}, 进度记录={}",
                  questions.len(), progress.total_questions);
            return Err(crate::error::Error::invalid_input(
                "题目数量与进度记录不一致，请重新开始模块"
            ));
        }

        // 2. 验证答题记录的题目ID是否都在题目列表中
        let question_ids: std::collections::HashSet<i32> = questions.iter()
            .map(|q| q.question_id)
            .collect();

        for answer in answers {
            if !question_ids.contains(&answer.question_id) {
                warn!("发现无效的答题记录: 题目ID={} 不在当前模块中", answer.question_id);
                return Err(crate::error::Error::invalid_input(
                    "发现无效的答题记录，请重新开始模块"
                ));
            }
        }

        // 3. 验证答题数量与进度记录一致性
        if answers.len() as i32 != progress.answered_questions {
            warn!("答题数量不一致: 实际答题={}, 进度记录={}",
                  answers.len(), progress.answered_questions);
            return Err(crate::error::Error::invalid_input(
                "答题数量与进度记录不一致，数据可能已损坏"
            ));
        }

        // 4. 验证模块类型一致性
        for answer in answers {
            if answer.module_type != progress.module_type {
                warn!("答题记录模块类型不一致: 答题记录={}, 进度记录={}",
                      answer.module_type, progress.module_type);
                return Err(crate::error::Error::invalid_input(
                    "答题记录模块类型不一致，请重新开始模块"
                ));
            }
        }

        // 5. 验证会话ID一致性
        for answer in answers {
            if answer.session_id != progress.session_id {
                warn!("答题记录会话ID不一致: 答题记录={}, 进度记录={}",
                      answer.session_id, progress.session_id);
                return Err(crate::error::Error::invalid_input(
                    "答题记录会话ID不一致，请重新开始模块"
                ));
            }
        }

        info!("数据一致性验证通过: 题目数={}, 答题数={}, 模块类型={}",
              questions.len(), answers.len(), progress.module_type);
        Ok(())
    }

    /// 计算SAT模块分数（简化版本）
    fn calculate_sat_module_score(&self, correct: i32, total: i32, subject: Subject) -> i32 {
        if total == 0 {
            return 200; // 最低分
        }

        let accuracy = correct as f64 / total as f64;

        // 简化的SAT分数计算
        // 实际SAT分数计算涉及复杂的等值化过程
        match subject {
            Subject::Math => {
                // 数学分数范围 200-800
                (200.0 + accuracy * 600.0) as i32
            }
            Subject::Reading => {
                // 语言分数范围 200-800
                (200.0 + accuracy * 600.0) as i32
            }
        }
    }

    /// 计算分数区间
    fn calculate_score_range(&self, sat_score: i32) -> ScoreRangeDto {
        // 简化的分数区间计算
        // 实际SAT会提供分数区间来反映测量误差
        let margin = 30; // ±30分的误差范围
        ScoreRangeDto {
            min_score: (sat_score - margin).max(200),
            max_score: (sat_score + margin).min(800),
        }
    }

    /// 获取学科名称
    fn get_subject_name(&self, subject: Subject) -> String {
        match subject {
            Subject::Math => "数学".to_string(),
            Subject::Reading => "语言".to_string(),
        }
    }
}
