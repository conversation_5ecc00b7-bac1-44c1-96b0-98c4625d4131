#!/usr/bin/env python3
"""
SAT考试API完整流程测试脚本

测试整个考试流程：
1. 获取用户状态
2. 获取考试指引
3. 创建考试会话
4. 开始模块
5. 提交答案
6. 提交模块
7. 提交整个考试

使用方法:
python test_exam_api.py
"""

import requests
import json
import time
import sys
from typing import Dict, Any, Optional

# API基础配置
BASE_URL = "http://localhost:8000/api/v1"
EXAM_API_BASE = f"{BASE_URL}/exam"

# 测试用户和试卷配置
TEST_USER_ID = 1
TEST_PAPER_ID = 1
EXAM_TYPE = "practice"  # 或 "official"

class ExamAPITester:
    def __init__(self):
        self.session_id: Optional[str] = None
        self.current_module_type = None
        self.questions = []
        
    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def make_request(self, method: str, url: str, data: Dict[Any, Any] = None, params: Dict[str, Any] = None) -> Dict[Any, Any]:
        """发送HTTP请求"""
        try:
            self.log(f"发送 {method} 请求到: {url}")
            if data:
                self.log(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            if params:
                self.log(f"请求参数: {json.dumps(params, indent=2, ensure_ascii=False)}")
                
            if method.upper() == "GET":
                response = requests.get(url, params=params, timeout=30)
            elif method.upper() == "POST":
                response = requests.post(url, json=data, params=params, timeout=30)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")
                
            self.log(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                self.log(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return result
            else:
                self.log(f"请求失败: {response.status_code} - {response.text}", "ERROR")
                return {"success": False, "error": response.text}
                
        except Exception as e:
            self.log(f"请求异常: {str(e)}", "ERROR")
            return {"success": False, "error": str(e)}
    
    def test_get_user_status(self) -> bool:
        """测试获取用户状态"""
        self.log("=== 测试获取用户状态 ===")
        
        url = f"{EXAM_API_BASE}/user/status"
        params = {"user_id": TEST_USER_ID}
        
        result = self.make_request("GET", url, params=params)
        
        if result.get("success"):
            self.log("✅ 获取用户状态成功")
            return True
        else:
            self.log("❌ 获取用户状态失败", "ERROR")
            return False
    
    def test_get_exam_guide(self) -> bool:
        """测试获取考试指引"""
        self.log("=== 测试获取考试指引 ===")
        
        url = f"{EXAM_API_BASE}/guide"
        params = {"exam_type": EXAM_TYPE}
        
        result = self.make_request("GET", url, params=params)
        
        if result.get("success"):
            self.log("✅ 获取考试指引成功")
            return True
        else:
            self.log("❌ 获取考试指引失败", "ERROR")
            return False
    
    def test_create_session(self) -> bool:
        """测试创建考试会话"""
        self.log("=== 测试创建考试会话 ===")
        
        url = f"{EXAM_API_BASE}/session/create"
        data = {
            "user_id": TEST_USER_ID,
            "paper_id": TEST_PAPER_ID,
            "exam_type": EXAM_TYPE
        }
        
        result = self.make_request("POST", url, data=data)
        
        if result.get("success") and result.get("data", {}).get("session_id"):
            self.session_id = result["data"]["session_id"]
            self.log(f"✅ 创建考试会话成功，会话ID: {self.session_id}")
            return True
        else:
            self.log("❌ 创建考试会话失败", "ERROR")
            return False
    
    def test_resume_session(self) -> bool:
        """测试恢复考试会话"""
        if not self.session_id:
            self.log("❌ 没有会话ID，跳过恢复会话测试", "ERROR")
            return False
            
        self.log("=== 测试恢复考试会话 ===")
        
        url = f"{EXAM_API_BASE}/session/resume"
        params = {
            "session_id": self.session_id,
            "user_id": TEST_USER_ID
        }
        
        result = self.make_request("GET", url, params=params)
        
        if result.get("success"):
            self.log("✅ 恢复考试会话成功")
            return True
        else:
            self.log("❌ 恢复考试会话失败", "ERROR")
            return False
    
    def test_get_session_state(self) -> bool:
        """测试获取会话状态"""
        if not self.session_id:
            self.log("❌ 没有会话ID，跳过获取会话状态测试", "ERROR")
            return False
            
        self.log("=== 测试获取会话状态 ===")
        
        url = f"{EXAM_API_BASE}/session/state"
        params = {
            "session_id": self.session_id,
            "user_id": TEST_USER_ID
        }
        
        result = self.make_request("GET", url, params=params)
        
        if result.get("success"):
            self.log("✅ 获取会话状态成功")
            return True
        else:
            self.log("❌ 获取会话状态失败", "ERROR")
            return False
    
    def test_validate_session(self) -> bool:
        """测试验证会话"""
        if not self.session_id:
            self.log("❌ 没有会话ID，跳过验证会话测试", "ERROR")
            return False
            
        self.log("=== 测试验证会话 ===")
        
        url = f"{EXAM_API_BASE}/session/validate"
        params = {
            "session_id": self.session_id,
            "user_id": TEST_USER_ID
        }
        
        result = self.make_request("GET", url, params=params)
        
        if result.get("success"):
            self.log("✅ 验证会话成功")
            return True
        else:
            self.log("❌ 验证会话失败", "ERROR")
            return False
    
    def test_start_module(self, module_type: str = "reading_module_1") -> bool:
        """测试开始模块"""
        if not self.session_id:
            self.log("❌ 没有会话ID，跳过开始模块测试", "ERROR")
            return False
            
        self.log(f"=== 测试开始模块: {module_type} ===")
        
        url = f"{EXAM_API_BASE}/module/start"
        data = {
            "session_id": self.session_id,
            "user_id": TEST_USER_ID,
            "module_type": module_type
        }
        
        result = self.make_request("POST", url, data=data)
        
        if result.get("success"):
            self.current_module_type = module_type
            # 保存题目信息用于后续答题
            if result.get("data", {}).get("questions"):
                self.questions = result["data"]["questions"]
            self.log(f"✅ 开始模块成功: {module_type}")
            return True
        else:
            self.log(f"❌ 开始模块失败: {module_type}", "ERROR")
            return False
    
    def test_submit_answer(self, question_id: int, answer: str = "A", time_spent: int = 30) -> bool:
        """测试提交答案"""
        if not self.session_id or not self.current_module_type:
            self.log("❌ 没有会话ID或模块类型，跳过提交答案测试", "ERROR")
            return False
            
        self.log(f"=== 测试提交答案: 题目ID={question_id}, 答案={answer} ===")
        
        url = f"{EXAM_API_BASE}/answer/submit"
        data = {
            "session_id": self.session_id,
            "user_id": TEST_USER_ID,
            "question_id": question_id,
            "student_answer": answer,
            "time_spent_seconds": time_spent,
            "module_type": self.current_module_type,
            "question_sequence": 1
        }
        
        result = self.make_request("POST", url, data=data)
        
        if result.get("success"):
            self.log(f"✅ 提交答案成功: 题目ID={question_id}")
            return True
        else:
            self.log(f"❌ 提交答案失败: 题目ID={question_id}", "ERROR")
            return False
    
    def test_get_answer_history(self) -> bool:
        """测试获取答题历史"""
        if not self.session_id:
            self.log("❌ 没有会话ID，跳过获取答题历史测试", "ERROR")
            return False
            
        self.log("=== 测试获取答题历史 ===")
        
        url = f"{EXAM_API_BASE}/answer/history"
        params = {
            "session_id": self.session_id,
            "user_id": TEST_USER_ID
        }
        
        result = self.make_request("GET", url, params=params)
        
        if result.get("success"):
            self.log("✅ 获取答题历史成功")
            return True
        else:
            self.log("❌ 获取答题历史失败", "ERROR")
            return False
    
    def test_submit_module(self, force_submit: bool = False) -> bool:
        """测试提交模块"""
        if not self.session_id or not self.current_module_type:
            self.log("❌ 没有会话ID或模块类型，跳过提交模块测试", "ERROR")
            return False
            
        self.log(f"=== 测试提交模块: {self.current_module_type} ===")
        
        url = f"{EXAM_API_BASE}/module/submit"
        data = {
            "session_id": self.session_id,
            "user_id": TEST_USER_ID,
            "module_type": self.current_module_type,
            "force_submit": force_submit
        }
        
        result = self.make_request("POST", url, data=data)
        
        if result.get("success"):
            self.log(f"✅ 提交模块成功: {self.current_module_type}")
            return True
        else:
            self.log(f"❌ 提交模块失败: {self.current_module_type}", "ERROR")
            return False
    
    def test_submit_exam(self, force_submit: bool = False) -> bool:
        """测试提交整个考试"""
        if not self.session_id:
            self.log("❌ 没有会话ID，跳过提交考试测试", "ERROR")
            return False
            
        self.log("=== 测试提交整个考试 ===")
        
        url = f"{EXAM_API_BASE}/submit"
        data = {
            "session_id": self.session_id,
            "user_id": TEST_USER_ID,
            "force_submit": force_submit
        }
        
        result = self.make_request("POST", url, data=data)
        
        if result.get("success"):
            self.log("✅ 提交考试成功")
            return True
        else:
            self.log("❌ 提交考试失败", "ERROR")
            return False
    
    def test_cancel_session(self) -> bool:
        """测试取消考试会话"""
        if not self.session_id:
            self.log("❌ 没有会话ID，跳过取消会话测试", "ERROR")
            return False
            
        self.log("=== 测试取消考试会话 ===")
        
        url = f"{EXAM_API_BASE}/session/cancel"
        data = {
            "session_id": self.session_id,
            "user_id": TEST_USER_ID
        }
        
        result = self.make_request("POST", url, data=data)
        
        if result.get("success"):
            self.log("✅ 取消考试会话成功")
            return True
        else:
            self.log("❌ 取消考试会话失败", "ERROR")
            return False
    
    def run_complete_exam_flow(self):
        """运行完整的考试流程测试"""
        self.log("🚀 开始完整考试流程测试")
        
        test_results = []
        
        # 1. 获取用户状态
        test_results.append(("获取用户状态", self.test_get_user_status()))
        
        # 2. 获取考试指引
        test_results.append(("获取考试指引", self.test_get_exam_guide()))
        
        # 3. 创建考试会话
        test_results.append(("创建考试会话", self.test_create_session()))
        
        if self.session_id:
            # 4. 恢复考试会话
            test_results.append(("恢复考试会话", self.test_resume_session()))
            
            # 5. 获取会话状态
            test_results.append(("获取会话状态", self.test_get_session_state()))
            
            # 6. 验证会话
            test_results.append(("验证会话", self.test_validate_session()))
            
            # 7. 开始阅读模块
            test_results.append(("开始阅读模块", self.test_start_module("reading_module_1")))
            
            # 8. 提交几个答案（模拟答题）
            for i in range(3):
                question_id = 1000 + i  # 模拟题目ID
                answer = ["A", "B", "C"][i % 3]  # 模拟答案
                test_results.append((f"提交答案{i+1}", self.test_submit_answer(question_id, answer)))
                time.sleep(1)  # 模拟答题间隔
            
            # 9. 获取答题历史
            test_results.append(("获取答题历史", self.test_get_answer_history()))
            
            # 10. 提交阅读模块
            test_results.append(("提交阅读模块", self.test_submit_module(force_submit=True)))
            
            # 11. 开始数学模块
            test_results.append(("开始数学模块", self.test_start_module("math_module_1")))
            
            # 12. 提交数学模块答案
            for i in range(2):
                question_id = 2000 + i  # 模拟数学题目ID
                answer = str(i + 1)  # 模拟数学答案
                test_results.append((f"提交数学答案{i+1}", self.test_submit_answer(question_id, answer)))
                time.sleep(1)
            
            # 13. 提交数学模块
            test_results.append(("提交数学模块", self.test_submit_module(force_submit=True)))
            
            # 14. 提交整个考试
            test_results.append(("提交整个考试", self.test_submit_exam(force_submit=True)))
        
        # 输出测试结果汇总
        self.log("=" * 50)
        self.log("📊 测试结果汇总")
        self.log("=" * 50)
        
        passed = 0
        failed = 0
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            self.log(f"{test_name}: {status}")
            if result:
                passed += 1
            else:
                failed += 1
        
        self.log("=" * 50)
        self.log(f"总计: {len(test_results)} 个测试")
        self.log(f"通过: {passed} 个")
        self.log(f"失败: {failed} 个")
        self.log(f"成功率: {passed/len(test_results)*100:.1f}%")
        
        if failed == 0:
            self.log("🎉 所有测试通过！")
        else:
            self.log("⚠️  部分测试失败，请检查日志")
        
        return failed == 0

def main():
    """主函数"""
    print("SAT考试API完整流程测试")
    print("=" * 50)
    
    tester = ExamAPITester()
    success = tester.run_complete_exam_flow()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
