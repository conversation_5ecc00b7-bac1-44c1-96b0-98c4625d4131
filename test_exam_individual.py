#!/usr/bin/env python3
"""
SAT考试API单个接口测试脚本

用于测试单个考试API接口，方便调试和验证

使用方法:
python test_exam_individual.py [api_name]

支持的API:
- user_status: 获取用户状态
- exam_guide: 获取考试指引
- create_session: 创建考试会话
- resume_session: 恢复考试会话
- session_state: 获取会话状态
- validate_session: 验证会话
- start_module: 开始模块
- submit_answer: 提交答案
- answer_history: 获取答题历史
- submit_module: 提交模块
- submit_exam: 提交考试
- cancel_session: 取消会话
"""

import requests
import json
import sys
from typing import Dict, Any, Optional

# API基础配置
BASE_URL = "http://localhost:8000/api/v1"
EXAM_API_BASE = f"{BASE_URL}/exam"

# 测试数据配置
TEST_CONFIG = {
    "user_id": 1,
    "paper_id": 1,
    "exam_type": "practice",
    "session_id": "test-session-123",  # 用于需要session_id的测试
    "question_id": 1001,
    "module_type": "reading_module_1"
}

def log(message: str, level: str = "INFO"):
    """日志输出"""
    print(f"[{level}] {message}")

def make_request(method: str, url: str, data: Dict[Any, Any] = None, params: Dict[str, Any] = None) -> Dict[Any, Any]:
    """发送HTTP请求"""
    try:
        log(f"发送 {method} 请求到: {url}")
        if data:
            log(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        if params:
            log(f"请求参数: {json.dumps(params, indent=2, ensure_ascii=False)}")
            
        if method.upper() == "GET":
            response = requests.get(url, params=params, timeout=30)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, params=params, timeout=30)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")
            
        log(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            log(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
        else:
            log(f"请求失败: {response.status_code} - {response.text}")
            return {"success": False, "error": response.text}
            
    except Exception as e:
        log(f"请求异常: {str(e)}")
        return {"success": False, "error": str(e)}

def test_user_status():
    """测试获取用户状态"""
    log("=== 测试获取用户状态 ===")
    
    url = f"{EXAM_API_BASE}/user/status"
    params = {"user_id": TEST_CONFIG["user_id"]}
    
    return make_request("GET", url, params=params)

def test_exam_guide():
    """测试获取考试指引"""
    log("=== 测试获取考试指引 ===")
    
    url = f"{EXAM_API_BASE}/guide"
    params = {"exam_type": TEST_CONFIG["exam_type"]}
    
    return make_request("GET", url, params=params)

def test_create_session():
    """测试创建考试会话"""
    log("=== 测试创建考试会话 ===")
    
    url = f"{EXAM_API_BASE}/session/create"
    data = {
        "user_id": TEST_CONFIG["user_id"],
        "paper_id": TEST_CONFIG["paper_id"],
        "exam_type": TEST_CONFIG["exam_type"]
    }
    
    return make_request("POST", url, data=data)

def test_resume_session():
    """测试恢复考试会话"""
    log("=== 测试恢复考试会话 ===")
    
    url = f"{EXAM_API_BASE}/session/resume"
    params = {
        "session_id": TEST_CONFIG["session_id"],
        "user_id": TEST_CONFIG["user_id"]
    }
    
    return make_request("GET", url, params=params)

def test_session_state():
    """测试获取会话状态"""
    log("=== 测试获取会话状态 ===")
    
    url = f"{EXAM_API_BASE}/session/state"
    params = {
        "session_id": TEST_CONFIG["session_id"],
        "user_id": TEST_CONFIG["user_id"]
    }
    
    return make_request("GET", url, params=params)

def test_validate_session():
    """测试验证会话"""
    log("=== 测试验证会话 ===")
    
    url = f"{EXAM_API_BASE}/session/validate"
    params = {
        "session_id": TEST_CONFIG["session_id"],
        "user_id": TEST_CONFIG["user_id"]
    }
    
    return make_request("GET", url, params=params)

def test_start_module():
    """测试开始模块"""
    log("=== 测试开始模块 ===")
    
    url = f"{EXAM_API_BASE}/module/start"
    data = {
        "session_id": TEST_CONFIG["session_id"],
        "user_id": TEST_CONFIG["user_id"],
        "module_type": TEST_CONFIG["module_type"]
    }
    
    return make_request("POST", url, data=data)

def test_submit_answer():
    """测试提交答案"""
    log("=== 测试提交答案 ===")
    
    url = f"{EXAM_API_BASE}/answer/submit"
    data = {
        "session_id": TEST_CONFIG["session_id"],
        "user_id": TEST_CONFIG["user_id"],
        "question_id": TEST_CONFIG["question_id"],
        "student_answer": "A",
        "time_spent_seconds": 30,
        "module_type": TEST_CONFIG["module_type"],
        "question_sequence": 1
    }
    
    return make_request("POST", url, data=data)

def test_answer_history():
    """测试获取答题历史"""
    log("=== 测试获取答题历史 ===")
    
    url = f"{EXAM_API_BASE}/answer/history"
    params = {
        "session_id": TEST_CONFIG["session_id"],
        "user_id": TEST_CONFIG["user_id"]
    }
    
    return make_request("GET", url, params=params)

def test_submit_module():
    """测试提交模块"""
    log("=== 测试提交模块 ===")
    
    url = f"{EXAM_API_BASE}/module/submit"
    data = {
        "session_id": TEST_CONFIG["session_id"],
        "user_id": TEST_CONFIG["user_id"],
        "module_type": TEST_CONFIG["module_type"],
        "force_submit": True
    }
    
    return make_request("POST", url, data=data)

def test_submit_exam():
    """测试提交考试"""
    log("=== 测试提交考试 ===")
    
    url = f"{EXAM_API_BASE}/submit"
    data = {
        "session_id": TEST_CONFIG["session_id"],
        "user_id": TEST_CONFIG["user_id"],
        "force_submit": True
    }
    
    return make_request("POST", url, data=data)

def test_cancel_session():
    """测试取消会话"""
    log("=== 测试取消会话 ===")
    
    url = f"{EXAM_API_BASE}/session/cancel"
    data = {
        "session_id": TEST_CONFIG["session_id"],
        "user_id": TEST_CONFIG["user_id"]
    }
    
    return make_request("POST", url, data=data)

# API测试函数映射
API_TESTS = {
    "user_status": test_user_status,
    "exam_guide": test_exam_guide,
    "create_session": test_create_session,
    "resume_session": test_resume_session,
    "session_state": test_session_state,
    "validate_session": test_validate_session,
    "start_module": test_start_module,
    "submit_answer": test_submit_answer,
    "answer_history": test_answer_history,
    "submit_module": test_submit_module,
    "submit_exam": test_submit_exam,
    "cancel_session": test_cancel_session,
}

def show_usage():
    """显示使用说明"""
    print("SAT考试API单个接口测试脚本")
    print("=" * 50)
    print("使用方法:")
    print("  python test_exam_individual.py [api_name]")
    print()
    print("支持的API:")
    for api_name in API_TESTS.keys():
        print(f"  - {api_name}")
    print()
    print("示例:")
    print("  python test_exam_individual.py user_status")
    print("  python test_exam_individual.py create_session")

def test_all_apis():
    """测试所有API"""
    log("=== 测试所有考试API ===")
    
    results = {}
    for api_name, test_func in API_TESTS.items():
        log(f"\n开始测试: {api_name}")
        try:
            result = test_func()
            results[api_name] = result.get("success", False)
            if result.get("success"):
                log(f"✅ {api_name} 测试通过")
            else:
                log(f"❌ {api_name} 测试失败: {result.get('error', '未知错误')}")
        except Exception as e:
            results[api_name] = False
            log(f"❌ {api_name} 测试异常: {str(e)}")
        
        log("-" * 30)
    
    # 输出汇总
    log("\n📊 测试结果汇总:")
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for api_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        log(f"  {api_name}: {status}")
    
    log(f"\n总计: {total} 个API")
    log(f"通过: {passed} 个")
    log(f"失败: {total - passed} 个")
    log(f"成功率: {passed/total*100:.1f}%")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        show_usage()
        return
    
    api_name = sys.argv[1].lower()
    
    if api_name == "all":
        test_all_apis()
        return
    
    if api_name not in API_TESTS:
        log(f"❌ 不支持的API: {api_name}")
        show_usage()
        return
    
    # 执行指定的API测试
    log(f"开始测试API: {api_name}")
    try:
        result = API_TESTS[api_name]()
        if result.get("success"):
            log(f"✅ {api_name} 测试通过")
        else:
            log(f"❌ {api_name} 测试失败: {result.get('error', '未知错误')}")
    except Exception as e:
        log(f"❌ {api_name} 测试异常: {str(e)}")

if __name__ == "__main__":
    main()
